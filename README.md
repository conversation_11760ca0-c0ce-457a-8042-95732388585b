<div align="center">

# 📧 免费临时邮箱

<p align="center">
  <img src="https://img.shields.io/badge/Nuxt-3.x-00DC82?style=for-the-badge&logo=nuxt.js&logoColor=white" alt="Nuxt 3">
  <img src="https://img.shields.io/badge/Vue-3.x-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue 3">
  <img src="https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript">
  <img src="https://img.shields.io/badge/TailwindCSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white" alt="Tailwind CSS">
</p>

<p align="center">
  <strong>🚀 基于 Nuxt 3 的现代化临时邮箱服务</strong>
</p>

<p align="center">
  无需注册 • 即时生成 • 自动接收 • 隐私保护
</p>

</div>

---

## 🙏 重要说明

> **📢 本项目为自用版本，基于 [FreeTempMail](https://github.com/PennyJoly/FreeTempMail) 开源项目二次开发，感谢 [@PennyJoly](https://github.com/PennyJoly) 的原创贡献。  **

> 在原作的基础上新增了多域名配置，并添加了密码保护功能。



## 🚀 快速开始

### 📋 环境要求

- **Node.js** >= 18.0.0
- **npm** / **pnpm** / **yarn**
- **IMAP 邮箱账号** (用于接收转发邮件)

### 🛠️ 安装步骤

```bash
# 1. 克隆项目
git clone https://github.com/your-username/your-repo-name.git
cd your-repo-name

# 2. 安装依赖
npm install
# 或者使用 pnpm (推荐)
pnpm install

# 3. 配置环境变量
cp .env.example .env.dev

# 4. 启动开发服务器
npm run dev
```

🎉 打开浏览器访问 `http://localhost:3000` 即可使用！

## ⚙️ 环境配置

### 📝 基础配置

在 `.env.dev` 文件中配置以下参数：

```bash
# 应用配置
NUXT_PUBLIC_APP_NAME=Your App Name
NUXT_PUBLIC_BASE_URL=http://localhost:3000
NUXT_PUBLIC_DOMAIN_URL=your-domain.com

# 多域名支持 (用逗号分隔)
NUXT_PUBLIC_AVAILABLE_DOMAINS=domain1.com,domain2.com,domain3.com

# 密码保护 (可选)
ACCESS_PASSWORD=your-secure-password

# 服务端口
PORT=3000
```

### 📧 IMAP 邮箱配置

<details>
<summary><strong>📖 点击展开 IMAP 配置说明</strong></summary>

#### Gmail 配置示例
```bash
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your-app-password  # 需要应用专用密码
IMAP_TLS=true
```

#### QQ 邮箱配置示例
```bash
IMAP_HOST=imap.qq.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your-authorization-code  # 需要授权码
IMAP_TLS=true
```

#### Outlook 配置示例
```bash
IMAP_HOST=outlook.office365.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your-password
IMAP_TLS=true
```

</details>


## 📄 开源协议

本项目基于 [MIT License](LICENSE) 开源协议。

## 🔗 相关链接

- 🌟 [原项目 FreeTempMail](https://github.com/PennyJoly/FreeTempMail)
- 👨‍💻 [原作者 @PennyJoly](https://github.com/PennyJoly)
- 🚀 [NuxtPro 商业版](https://nuxtpro.com)

---

<div align="center">

### 💝 如果这个项目对您有帮助，请给个 ⭐ Star 支持一下！

**Made with ❤️ by developers, for developers**

</div>

