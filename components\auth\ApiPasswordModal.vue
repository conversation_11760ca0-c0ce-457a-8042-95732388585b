<template>
  <div v-if="showModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">设置 API 访问密码</h3>
      
      <p class="text-sm text-gray-600 mb-4">
        为了保护 API 接口，需要设置访问密码。请输入与服务端配置相同的密码。
      </p>
      
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">API 密码</label>
          <input
            v-model="password"
            type="password"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入 API 访问密码"
            :class="{ 'border-red-500': error }"
          />
          <p v-if="error" class="text-red-500 text-sm mt-1">{{ error }}</p>
        </div>
        
        <div class="flex gap-3">
          <button
            type="button"
            @click="handleCancel"
            class="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="!password.trim() || isLoading"
            class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ isLoading ? '验证中...' : '确认' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'success', password: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const password = ref('')
const error = ref('')
const isLoading = ref(false)

const showModal = computed(() => props.show)

const handleSubmit = async () => {
  if (!password.value.trim()) {
    error.value = '请输入密码'
    return
  }

  isLoading.value = true
  error.value = ''

  try {
    // 测试密码是否正确，尝试调用一个需要认证的 API
    const response = await $fetch('/api/tempmail/generate', {
      method: 'POST',
      headers: {
        'x-api-password': password.value.trim()
      }
    })

    // 如果成功，说明密码正确
    emit('success', password.value.trim())
  } catch (err: any) {
    if (err.statusCode === 401) {
      error.value = '密码错误，请重试'
    } else {
      error.value = '验证失败，请重试'
    }
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  password.value = ''
  error.value = ''
  emit('close')
}

// 监听 show 变化，重置表单
watch(() => props.show, (newShow) => {
  if (newShow) {
    password.value = ''
    error.value = ''
  }
})
</script>
