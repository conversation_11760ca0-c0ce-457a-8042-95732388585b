{"name": "FreeTempMail", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --dotenv .env.dev", "prod": "nuxt dev --dotenv .env.prod", "start:prod": "dotenv -e .env.prod -- node .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "build:dev": "nuxt build --dotenv .env.dev", "build:test": "nuxt build --dotenv .env.test", "build:prod": "nuxt build --dotenv .env.prod"}, "dependencies": {"@nuxtjs/plausible": "^1.2.0", "@nuxtjs/sitemap": "^7.2.10", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "imap": "^0.8.19", "mailparser": "^3.7.1", "lucide-vue-next": "^0.479.0", "marked": "^15.0.11", "nuxt": "^3.14.1592", "nuxt-llms": "^0.1.2", "reflect-metadata": "^0.2.2", "reka-ui": "^2.2.0", "shadcn-nuxt": "^1.0.3", "tailwind-merge": "^3.0.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "vue": "latest", "vue-chartjs": "^5.3.2", "vue-router": "latest", "zod": "^3.25.67"}, "devDependencies": {"@nuxtjs/i18n": "^9.0.0-rc.2", "@nuxtjs/tailwindcss": "^6.13.1", "@types/imap": "^0.8.40", "@types/mailparser": "^3.4.4", "autoprefixer": "^10.4.20", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.1", "postcss": "^8.5.1", "sass": "^1.81.0"}}