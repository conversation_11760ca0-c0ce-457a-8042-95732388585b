NODE_ENV=development
NUXT_PUBLIC_ENV=development
# your website url e.g: mail.aitre.cc
NUXT_PUBLIC_BASE_URL=
# your domain url e.g:aitre.cc
NUXT_PUBLIC_DOMAIN_URL=
# 支持的域名列表，用逗号分隔 e.g: domain1.com,domain2.com,domain3.com
NUXT_PUBLIC_AVAILABLE_DOMAINS=
# Server port
PORT=3000
# PLAUSIBLE statistics service domain
NUXT_PUBLIC_PLAUSIBLE_DOMAIN= # e.g: mail.aitre.cc
# PLAUSIBLE statistics service API address
NUXT_PUBLIC_PLAUSIBLE_API_HOST= # e.g: https://plausible.io/
# Google Search Console verifyCode
GOOGLE_SEARCH_CONSOLE_VERIFICATION_CODE=
# 隐私保护密码 - 设置后需要输入密码才能使用服务 (可选)
NUXT_PUBLIC_ACCESS_PASSWORD=
# IMAP server address
IMAP_HOST=imap.example.com
# IMAP port (usually 993 for SSL, 143 for non-SSL)
IMAP_PORT=993
# Your mailbox username
IMAP_USERNAME=<EMAIL>
# Your mailbox password
IMAP_PASSWORD=your-password
# Enable TLS encryption (true/false)
IMAP_TLS=true



