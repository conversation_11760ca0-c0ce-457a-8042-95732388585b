import type { ParsedMail } from 'mailparser'
import {
  type EmailData,
  extractPreview,
  extractContent,
  isEmailForTarget,
  formatDateForIMAP,
  formatFileSize
} from '~/server/utils/mail.utils'

// 简单的内存缓存来限制查询频率
const queryCache = new Map<string, { lastQuery: number, count: number }>()
const QUERY_LIMIT = 5 // 每小时最多查询5次
const QUERY_WINDOW = 60 * 60 * 1000 // 1小时

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { email: targetEmail } = body
  
  if (!targetEmail || typeof targetEmail !== 'string') {
    throw createError({
      statusCode: 400,
      statusMessage: '缺少目标邮箱地址参数'
    })
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(targetEmail)) {
    throw createError({
      statusCode: 400,
      statusMessage: '邮箱格式不正确'
    })
  }

  // 验证域名
  const emailDomain = targetEmail.split('@')[1]
  const allowedDomain = process.env.NUXT_PUBLIC_DOMAIN_URL
  
  if (emailDomain !== allowedDomain) {
    throw createError({
      statusCode: 403,
      statusMessage: `只能查询 @${allowedDomain} 域名的邮箱`
    })
  }

  // 获取客户端IP进行频率限制
  const clientIP = getClientIP(event) || 'unknown'
  const now = Date.now()
  const cacheKey = `${clientIP}:${targetEmail}`
  
  // 检查查询频率
  const cached = queryCache.get(cacheKey)
  if (cached) {
    if (now - cached.lastQuery < QUERY_WINDOW) {
      if (cached.count >= QUERY_LIMIT) {
        throw createError({
          statusCode: 429,
          statusMessage: '查询过于频繁，请稍后再试'
        })
      }
      cached.count++
    } else {
      // 重置计数器
      cached.lastQuery = now
      cached.count = 1
    }
  } else {
    queryCache.set(cacheKey, { lastQuery: now, count: 1 })
  }

  // 清理过期的缓存条目
  for (const [key, value] of queryCache.entries()) {
    if (now - value.lastQuery > QUERY_WINDOW) {
      queryCache.delete(key)
    }
  }

  // 从环境变量获取IMAP配置
  const imapConfig = {
    user: process.env.IMAP_USERNAME || '',
    password: process.env.IMAP_PASSWORD || '',
    host: process.env.IMAP_HOST || '',
    port: parseInt(process.env.IMAP_PORT || '993'),
    tls: process.env.IMAP_TLS !== 'false',
    tlsOptions: { rejectUnauthorized: false }
  }

  if (!imapConfig.user || !imapConfig.password || !imapConfig.host) {
    throw createError({
      statusCode: 500,
      statusMessage: 'IMAP配置未完成，请先配置邮箱连接参数'
    })
  }

  try {
    const emails = await fetchEmailsFromIMAP(imapConfig, targetEmail)
    return {
      success: true,
      emails,
      count: emails.length,
      message: `找到 ${emails.length} 封邮件`
    }
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: `查询邮件失败: ${(error as Error).message}`
    })
  }
})

async function fetchEmailsFromIMAP(config: any, targetEmail: string): Promise<EmailData[]> {
  const Imap = await import('imap').then(m => m.default)
  const { simpleParser } = await import('mailparser') as any
  
  return new Promise((resolve, reject) => {
    const imap = new Imap(config)
    const emails: EmailData[] = []

    imap.once('ready', () => {
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          reject(err)
          return
        }

        // 搜索最近3天的邮件，减少查询范围
        const threeDaysAgo = new Date()
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
        const searchCriteria = [['SINCE', formatDateForIMAP(threeDaysAgo)]]
        
        imap.search(searchCriteria, (err, results) => {
          if (err) {
            reject(err)
            return
          }

          if (!results || results.length === 0) {
            imap.end()
            resolve([])
            return
          }

          // 限制处理的邮件数量
          const limitedResults = results.slice(-20) // 只处理最新的20封邮件

          const fetch = imap.fetch(limitedResults, {
            bodies: '',
            markSeen: false,
            struct: true
          })

          let processedCount = 0

          fetch.on('message', (msg, seqno) => {
            let buffer = ''
            let uid = ''
            msg.on('attributes', (attrs) => {
              uid = attrs.uid
            })
            
            msg.on('body', (stream, info) => {
              stream.on('data', (chunk) => {
                buffer += chunk.toString('utf8')
              })
              
              stream.once('end', () => {
                simpleParser(buffer)
                  .then((parsed: ParsedMail) => {
                    if (isEmailForTarget(parsed, targetEmail)) {
                      const emailData: EmailData = {
                        id: uid,
                        subject: parsed.subject || '无主题',
                        sender: parsed.from?.text || '未知发送者',
                        preview: extractPreview(parsed),
                        content: extractContent(parsed),
                        receivedAt: parsed.date || new Date(),
                        read: false,
                        hasAttachment: (parsed.attachments?.length || 0) > 0,
                        attachments: parsed.attachments?.map((att: any) => ({
                          name: att.filename || '未知文件',
                          size: formatFileSize(att.size || 0)
                        }))
                      }
                      emails.push(emailData)
                    }
                    
                    processedCount++
                    
                    if (processedCount === limitedResults.length) {
                      imap.end()
                      emails.sort((a, b) => b.receivedAt.getTime() - a.receivedAt.getTime())
                      resolve(emails)
                    }
                  })
                  .catch((error: any) => {
                    console.error('解析邮件失败:', error)
                    processedCount++
                    
                    if (processedCount === limitedResults.length) {
                      imap.end()
                      emails.sort((a, b) => b.receivedAt.getTime() - a.receivedAt.getTime())
                      resolve(emails)
                    }
                  })
              })
            })
          })

          fetch.once('error', reject)
          fetch.once('end', () => {
            if (processedCount === 0) {
              imap.end()
              resolve([])
            }
          })
        })
      })
    })

    imap.once('error', reject)
    
    // 设置连接超时
    setTimeout(() => {
      if (imap.state !== 'disconnected') {
        imap.end()
        reject(new Error('IMAP连接超时'))
      }
    }, 30000) // 30秒超时
    
    imap.connect()
  })
}
